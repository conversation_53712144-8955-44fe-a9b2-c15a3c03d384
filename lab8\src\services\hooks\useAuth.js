import { useEffect, useState } from "react";
import { auth } from "../services/firebase";
import {
  createUserWithEmailAndPassword, signInWithEmailAndPassword,
  signOut, onAuthStateChanged
} from "firebase/auth";

export function useAuth() {
  const [user, setUser] = useState(null);
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, u => {
      setUser(u);
      if (initializing) setInitializing(false);
    });
    return unsubscribe;
  }, [initializing]);

  const signup = (email, pw) => createUserWithEmailAndPassword(auth, email, pw);
  const login = (email, pw) => signInWithEmailAndPassword(auth, email, pw);
  const logout = () => signOut(auth);

  return { user, initializing, signup, login, logout };
}
