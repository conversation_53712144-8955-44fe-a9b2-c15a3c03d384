import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Alert, StyleSheet, Pressable } from 'react-native';
import AttemptProgressBar from '../components/AttemptProgressBar';

const GameScreen = ({ navigation }) => {
  const [randomNumber, setRandomNumber] = useState(0);
  const [guess, setGuess] = useState('');
  const [attemptsLeft, setAttemptsLeft] = useState(5);
  const [message, setMessage] = useState('');
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState([]);

  useEffect(() => {
    startNewGame();
  }, []);

  const startNewGame = () => {
    setRandomNumber(Math.floor(Math.random() * 25) + 1);
    setGuess('');
    setAttemptsLeft(5);
    setMessage('');
    setProgress(0);
  };

  const handleGuess = () => {
    const numGuess = parseInt(guess);
    if (isNaN(numGuess) || numGuess < 1 || numGuess > 25) {
      setMessage('Please enter a number between 1 and 25');
      return;
    }

    const newAttemptsLeft = attemptsLeft - 1;
    const currentProgress = ((5 - newAttemptsLeft) / 5);
    setAttemptsLeft(newAttemptsLeft);
    setProgress(currentProgress);

    if (numGuess === randomNumber) {
      Alert.alert('You Win!', `You guessed it in ${5 - newAttemptsLeft} attempts.`, [
        { text: 'Play Again', onPress: () => handleResult(true, 5 - newAttemptsLeft) },
        { text: 'View Scoreboard', onPress: () => goToScoreboard(true, 5 - newAttemptsLeft) },
      ]);
    } else {
      if (newAttemptsLeft === 0) {
        Alert.alert('Game Over', 'You have no attempts left.', [
          { text: 'Play Again', onPress: () => handleResult(false, 5) },
          { text: 'View Scoreboard', onPress: () => goToScoreboard(false, 5) },
        ]);
      } else {
        setMessage(numGuess > randomNumber ? 'Too High!' : 'Too Low!');
      }
    }
    setGuess('');
  };

  const handleResult = (won, attemptsUsed) => {
    const newResults = [...results, { result: won, attempts: attemptsUsed }];
    setResults(newResults);
    startNewGame();
  };

  const goToScoreboard = (won, attemptsUsed) => {
    const newResults = [...results, { result: won, attempts: attemptsUsed }];
    setResults(newResults);
    navigation.navigate('Scoreboard', { gameResults: newResults });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Raj Patel - N012345678</Text>
      <AttemptProgressBar progress={progress} />
      <TextInput
        style={styles.input}
        placeholder="Enter your guess"
        keyboardType="numeric"
        value={guess}
        onChangeText={setGuess}
      />
      <Pressable style={styles.button} onPress={handleGuess}>
        <Text style={styles.buttonText}>Guess</Text>
      </Pressable>
      <Text style={styles.message}>{message}</Text>
      <Text style={styles.attempts}>Attempts left: {attemptsLeft}</Text>
    </View>
  );
};

export default GameScreen;

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, justifyContent: 'center' },
  header: { fontSize: 18, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 },
  input: { borderWidth: 1, borderColor: '#ccc', padding: 10, marginBottom: 10 },
  button: { backgroundColor: '#1e90ff', padding: 12, alignItems: 'center', borderRadius: 10 },
  buttonText: { color: 'white', fontWeight: 'bold' },
  message: { fontSize: 16, marginVertical: 10 },
  attempts: { fontSize: 16, marginTop: 5 }
});
