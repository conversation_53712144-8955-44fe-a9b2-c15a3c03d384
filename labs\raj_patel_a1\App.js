import React, { useState } from 'react';
import { View, Text, TextInput, Button, Alert, StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';

const App = () => {
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');

  const calculateBMI = () => {
    const w = parseFloat(weight);
    const h = parseFloat(height);

    if (isNaN(w) || isNaN(h)) {
      Alert.alert("INVALID INPUT", "PLEASE ENTER VALID NUMBERS FOR BOTH FIELDS.");
      return;
    }

    if (w <= 0) {
      Alert.alert("INVALID WEIGHT", "WEIGHT MUST BE GREATER THAN 0 KG.");
      return;
    }

    if (h <= 0 || h > 3) {
      Alert.alert("INVALID HEIGHT", "HEIGHT MUST BE BETWEEN 0 AND 3 METERS.");
      return;
    }

    const bmi = (w / (h * h)).toFixed(1);

    let category = "";
    if (bmi < 18.5) category = "UNDERWEIGHT";
    else if (bmi < 25) category = "NORMAL WEIGHT";
    else if (bmi < 30) category = "OVERWEIGHT";
    else category = "OBESITY";

    Alert.alert("BMI RESULT", `YOUR BMI IS ${bmi} (${category})`);
  };

  const resetFields = () => {
    setWeight('');
    setHeight('');
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={styles.container}>
      <Text style={[styles.heading, styles.uppercase]}>Bmi Calculator</Text>

      <Text style={[styles.label, styles.uppercase]}>Weight (kg):</Text>
      <TextInput
        style={styles.input}
        placeholder="ENTER WEIGHT IN KG"
        placeholderTextColor="#888"
        value={weight}
        onChangeText={setWeight}
        keyboardType="numeric"
      />

      <Text style={[styles.label, styles.uppercase]}>Height (m):</Text>
      <TextInput
        style={styles.input}
        placeholder="ENTER HEIGHT IN METERS"
        placeholderTextColor="#888"
        value={height}
        onChangeText={setHeight}
        keyboardType="numeric"
      />

      <View style={styles.buttonContainer}>
        <View style={styles.buttonWrapper}>
          <Button title="CALCULATE" onPress={calculateBMI} />
        </View>
        <View style={styles.buttonWrapper}>
          <Button title="RESET" onPress={resetFields} color="gray" />
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  uppercase: {
    textTransform: 'uppercase',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#f8f8f8',
  },
  heading: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
    color: '#333',
  },
  label: {
    fontWeight: 'bold',
    color: '#333',
    marginTop: 12,
    marginBottom: 4,
    fontSize: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  buttonContainer: {
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonWrapper: {
    flex: 1,
    marginHorizontal: 5,
  }
});

export default App;
