import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

export default function EventCard({ event, onPress }: any) {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <Text style={styles.name}>{event.name}</Text>
      <Text>{event.dates.start.localDate}</Text>
      <Text>{event._embedded?.venues[0]?.name}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: { backgroundColor: '#f0f0f0', marginBottom: 10, padding: 10, borderRadius: 8 },
  name: { fontWeight: 'bold' },
});
