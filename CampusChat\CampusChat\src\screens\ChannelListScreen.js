import React, { useCallback, useEffect, useState } from 'react';
import { View, FlatList, StyleSheet, ActivityIndicator, Alert, TouchableOpacity, Text } from 'react-native';
import { firestore } from '../services/firebase';
import ChannelItem from '../components/ChannelItem';
import { signOut } from '../services/auth';

const DEFAULT_CHANNELS = [
  { name: '#general', description: 'General chat for everyone' },
  { name: '#study-groups', description: 'Coordinate study sessions' },
  { name: '#announcements', description: 'Important updates' },
  { name: '#random', description: 'Anything goes' },
];

export default function ChannelListScreen({ navigation }) {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);

  // Seed default channels if collection is empty
  const seedDefaultsIfNeeded = useCallback(async () => {
    const snap = await firestore().collection('channels').limit(1).get();
    if (snap.empty) {
      const batch = firestore().batch();
      DEFAULT_CHANNELS.forEach(ch => {
        const ref = firestore().collection('channels').doc();
        batch.set(ref, { ...ch, createdAt: firestore.FieldValue.serverTimestamp() });
      });
      await batch.commit();
    }
  }, []);

  useEffect(() => {
    (async () => {
      try {
        await seedDefaultsIfNeeded();
        const unsub = firestore()
          .collection('channels')
          .orderBy('createdAt', 'asc')
          .onSnapshot((snapshot) => {
            const data = snapshot.docs.map(d => ({ id: d.id, ...d.data() }));
            setChannels(data);
            setLoading(false);
          });
        return () => unsub();
      } catch (e) {
        console.error(e);
        Alert.alert('Error', 'Could not load channels');
        setLoading(false);
      }
    })();
  }, [seedDefaultsIfNeeded]);

  const openChannel = (channel) => navigation.navigate('Chat', { channel });

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Channels</Text>
        <TouchableOpacity onPress={() => signOut()}>
          <Text style={styles.signOut}>Sign Out</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={channels}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <ChannelItem channel={item} onPress={() => openChannel(item)} />}
        ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
        contentContainerStyle={{ padding: 16 }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  header: { padding: 16, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  headerTitle: { fontSize: 20, fontWeight: '800' },
  signOut: { color: '#ef4444', fontWeight: '700' },
});
