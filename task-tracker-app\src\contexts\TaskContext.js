import React, { createContext, useEffect, useReducer } from 'react';
import { taskReducer, initialState } from '../reducers/taskReducer';
import { loadTasksFromStorage, saveTasksToStorage } from '../utils/storage';

export const TaskContext = createContext();

export const TaskProvider = ({ children }) => {
  const [state, dispatch] = useReducer(taskReducer, initialState);

  useEffect(() => {
    loadTasksFromStorage(dispatch);
  }, []);

  useEffect(() => {
    saveTasksToStorage(state.tasks);
  }, [state.tasks]);

  return (
    <TaskContext.Provider value={{ state, dispatch }}>
      {children}
    </TaskContext.Provider>
  );
};
