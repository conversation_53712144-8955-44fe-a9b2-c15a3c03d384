import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import FadeInView from '../animations/FadeInView';

const RecipeCard = ({ recipe }) => (
  <FadeInView style={styles.card}>
    <Image source={{ uri: recipe.image }} style={styles.image} />
    <Text style={styles.title}>{recipe.title}</Text>
  </FadeInView>
);

const styles = StyleSheet.create({
  card: {
    margin: 10,
    borderRadius: 15,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 3,
  },
  image: {
    height: 200,
    width: '100%',
  },
  title: {
    padding: 10,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default RecipeCard;
