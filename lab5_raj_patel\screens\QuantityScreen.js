import React, { useState } from 'react';
import { View, Text, TextInput, Button, Alert, StyleSheet } from 'react-native';

const QuantityScreen = ({ navigation }) => {
  const [quantity, setQuantity] = useState('');

  const handleNext = () => {
    const qty = parseInt(quantity);
    if (isNaN(qty) || qty <= 0) {
      Alert.alert('Invalid Input', 'Please enter a valid positive number.');
      return;
    }
    navigation.navigate('Price', { quantity: qty });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Enter Quantity:</Text>
      <TextInput
        style={styles.input}
        keyboardType="numeric"
        value={quantity}
        onChangeText={setQuantity}
        placeholder="e.g. 3"
      />
      <Button title="Next" onPress={handleNext} />
    </View>
  );
};

export default QuantityScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1, justifyContent: 'center', padding: 20,
  },
  label: {
    fontSize: 18, marginBottom: 10,
  },
  input: {
    borderWidth: 1, borderColor: '#ccc', padding: 10, marginBottom: 20, borderRadius: 5,
  }
});
