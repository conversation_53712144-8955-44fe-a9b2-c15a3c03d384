import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import CircularProgress from '../animations/CircularProgress';

const RecipeDetail = ({ route }) => {
  const { recipe } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{recipe.title}</Text>
      <CircularProgress fill={65} title="Cooking Timer" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});

export default RecipeDetail;
