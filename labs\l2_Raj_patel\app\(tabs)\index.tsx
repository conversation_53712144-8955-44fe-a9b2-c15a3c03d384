import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert } from 'react-native';

export default function App() {
  // State variables for avatar name and points
  const [avatarName, setAvatarName] = useState('');
  const [points, setPoints] = useState('');

  // Function to handle the Enter button click
  const handleEnter = () => {
    // Validate inputs
    if (!avatarName.trim()) {
      Alert.alert('Error', 'Please enter an avatar name.');
      return;
    }
    if (!points || isNaN(points) || parseInt(points) < 0) {
      Alert.alert('Error', 'Please enter a valid number of points (non-negative integer).');
      return;
    }

    // Display alert with user information
    Alert.alert(
      'Player Information',
      `Avatar Name: ${avatarName}\nPoints: ${points}`,
      [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Game Onboarding Form</Text>

      {/* Avatar Name Input */}
      <Text style={styles.label}>Gaming Avatar Name</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter avatar name"
        value={avatarName}
        onChangeText={setAvatarName}
      />

      {/* Points Input */}
      <Text style={styles.label}>Number of Points</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter points"
        value={points}
        onChangeText={setPoints}
        keyboardType="numeric"
      />

      {/* Enter Button */}
      <TouchableOpacity style={styles.button} onPress={handleEnter}>
        <Text style={styles.buttonText}>Enter</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
    marginBottom: 30,
  },
  label: {
    fontSize: 18,
    fontWeight: '600',
    color: '#34495e',
    marginBottom: 10,
  },
  input: {
    height: 50,
    borderColor: '#3498db',
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    marginBottom: 20,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#e74c3c',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },}
);