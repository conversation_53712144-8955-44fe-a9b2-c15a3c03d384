import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import QuantityScreen from './screens/QuantityScreen';
import PriceScreen from './screens/PriceScreen';
import FinalBillScreen from './screens/FinalBillScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Quantity">
        <Stack.Screen name="Quantity" component={QuantityScreen} />
        <Stack.Screen name="Price" component={PriceScreen} />
        <Stack.Screen name="FinalBill" component={FinalBillScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
