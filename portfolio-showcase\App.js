import React, { useState, useEffect, createContext, useContext } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import HomeScreen from './screens/HomeScreen';
import GalleryScreen from './screens/GalleryScreen';
import AboutScreen from './screens/AboutScreen';
import { DarkModeToggle } from './components/DarkModeToggle';
import { View, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const Tab = createBottomTabNavigator();

export const ThemeContext = createContext();

export default function App() {
  const [isDark, setIsDark] = useState(false);

  return (
    <ThemeContext.Provider value={{ isDark, setIsDark }}>
      <SafeAreaProvider>
        <NavigationContainer theme={isDark ? DarkTheme : DefaultTheme}>
          <Tab.Navigator
            screenOptions={({ route }) => ({
              tabBarIcon: ({ color, size }) => {
                let iconName;
                if (route.name === 'Home') iconName = 'home';
                else if (route.name === 'Gallery') iconName = 'photo-library';
                else if (route.name === 'About') iconName = 'person';
                return <MaterialIcons name={iconName} size={size} color={color} />;
              }
            })}
          >
            <Tab.Screen
              name="Home"
              component={HomeScreen}
              options={{
                headerRight: () => (
                  <View style={styles.toggle}>
                    <DarkModeToggle />
                  </View>
                )
              }}
            />
            <Tab.Screen name="Gallery" component={GalleryScreen} />
            <Tab.Screen name="About" component={AboutScreen} />
          </Tab.Navigator>
          <StatusBar style={isDark ? 'light' : 'dark'} />
        </NavigationContainer>
      </SafeAreaProvider>
    </ThemeContext.Provider>
  );
}

const styles = StyleSheet.create({
  toggle: {
    marginRight: 15
  }
});
