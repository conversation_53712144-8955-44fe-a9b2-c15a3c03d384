import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  FlatList,
  StyleSheet,
} from 'react-native';
import useTasks from '../hooks/useTasks';
import TaskItem from '../components/TaskItem';

export default function TaskList() {
  const { state, dispatch } = useTasks();
  const [newTask, setNewTask] = useState('');
  const [filter, setFilter] = useState('all');

  const filteredTasks = state.tasks.filter(task => {
    if (filter === 'all') return true;
    if (filter === 'completed') return task.completed;
    return !task.completed;
  });

  const handleAddTask = () => {
    if (!newTask.trim()) return;
    dispatch({
      type: 'ADD_TASK',
      payload: {
        id: Date.now().toString(),
        title: newTask,
        completed: false,
      },
    });
    setNewTask('');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Task Tracker</Text>
      <TextInput
        value={newTask}
        onChangeText={setNewTask}
        placeholder="Enter task"
        style={styles.input}
      />
      <Button title="Add Task" onPress={handleAddTask} />
      <View style={styles.filters}>
        <Button title="All" onPress={() => setFilter('all')} />
        <Button title="Active" onPress={() => setFilter('active')} />
        <Button title="Completed" onPress={() => setFilter('completed')} />
      </View>
      <FlatList
        data={filteredTasks}
        keyExtractor={item => item.id}
        renderItem={({ item }) => <TaskItem task={item} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginTop: 40,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  input: {
    borderBottomWidth: 1,
    marginBottom: 10,
  },
  filters: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 10,
  },
});
