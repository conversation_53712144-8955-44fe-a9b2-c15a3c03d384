import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const FinalBillScreen = ({ route }) => {
  const { price } = route.params;
  const tax = price * 0.13;
  const total = price + tax;

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Price: ${price.toFixed(2)}</Text>
      <Text style={styles.text}>Tax (13%): ${tax.toFixed(2)}</Text>
      <Text style={styles.text}>Final Total: ${total.toFixed(2)}</Text>
    </View>
  );
};

export default FinalBillScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1, justifyContent: 'center', alignItems: 'center',
  },
  text: {
    fontSize: 20, marginBottom: 15,
  },
});
