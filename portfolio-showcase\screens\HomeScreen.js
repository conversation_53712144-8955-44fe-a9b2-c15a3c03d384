import React from 'react';
import { View, Text, StyleSheet, ScrollView, Linking, TouchableOpacity } from 'react-native';
import { SocialLinks } from '../components/SocialLinks';

export default function HomeScreen() {
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.heading}>Welcome to My Portfolio!</Text>
      <Text style={styles.paragraph}>
        Hi, I'm a passionate creative professional. Check out my projects and feel free to get in touch.
      </Text>
      <SocialLinks />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: 'transparent'
  },
  heading: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center'
  },
  paragraph: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center'
  }
});
