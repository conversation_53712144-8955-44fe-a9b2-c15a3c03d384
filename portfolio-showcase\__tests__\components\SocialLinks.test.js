import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Linking } from 'react-native';
import { SocialLinks } from '../../components/SocialLinks';

// Mock Linking
jest.mock('react-native/Libraries/Linking/Linking', () => ({
  openURL: jest.fn()
}));

describe('SocialLinks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly', () => {
    const { getByLabelText } = render(<SocialLinks />);
    
    expect(getByLabelText('Open github profile')).toBeTruthy();
    expect(getByLabelText('Open linkedin profile')).toBeTruthy();
    expect(getByLabelText('Open twitter profile')).toBeTruthy();
  });

  test('opens GitHub URL when GitHub icon is pressed', () => {
    const { getByLabelText } = render(<SocialLinks />);
    const githubButton = getByLabelText('Open github profile');
    
    fireEvent.press(githubButton);
    expect(Linking.openURL).toHaveBeenCalledWith('https://github.com/yourusername');
  });

  test('opens LinkedIn URL when LinkedIn icon is pressed', () => {
    const { getByLabelText } = render(<SocialLinks />);
    const linkedinButton = getByLabelText('Open linkedin profile');
    
    fireEvent.press(linkedinButton);
    expect(Linking.openURL).toHaveBeenCalledWith('https://linkedin.com/in/yourprofile');
  });

  test('opens Twitter URL when Twitter icon is pressed', () => {
    const { getByLabelText } = render(<SocialLinks />);
    const twitterButton = getByLabelText('Open twitter profile');
    
    fireEvent.press(twitterButton);
    expect(Linking.openURL).toHaveBeenCalledWith('https://twitter.com/yourhandle');
  });
});
