import React, { createContext, useContext, useEffect, useState } from 'react';
import { auth, firestore } from './firebase';

const AuthContext = createContext(null);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    const unsub = auth().onAuthStateChanged(async (firebaseUser) => {
      setUser(firebaseUser);
      if (initializing) setInitializing(false);

      // Ensure a user document exists
      if (firebaseUser) {
        const userDocRef = firestore().collection('users').doc(firebaseUser.uid);
        const snap = await userDocRef.get();
        if (!snap.exists) {
          await userDocRef.set({
            email: firebaseUser.email ?? '',
            username: firebaseUser.displayName ?? firebaseUser.email?.split('@')[0] ?? 'user',
            createdAt: firestore.FieldValue.serverTimestamp()
          });
        }
      }
    });
    return unsub;
  }, [initializing]);

  return (
    <AuthContext.Provider value={{ user, initializing }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}

export async function signUpWithEmail({ email, password, username }) {
  const cred = await auth().createUserWithEmailAndPassword(email.trim(), password);
  await cred.user.updateProfile({ displayName: username });
  await firestore().collection('users').doc(cred.user.uid).set({
    email: cred.user.email,
    username,
    createdAt: firestore.FieldValue.serverTimestamp()
  });
  return cred.user;
}

export function signInWithEmail({ email, password }) {
  return auth().signInWithEmailAndPassword(email.trim(), password);
}

export function signOut() {
  return auth().signOut();
}
