import AsyncStorage from '@react-native-async-storage/async-storage';

export const saveTasksToStorage = async (tasks) => {
  try {
    await AsyncStorage.setItem('TASKS', JSON.stringify(tasks));
  } catch (error) {
    console.error('Failed to save tasks:', error);
  }
};

export const loadTasksFromStorage = async (dispatch) => {
  try {
    const tasks = await AsyncStorage.getItem('TASKS');
    if (tasks !== null) {
      dispatch({ type: 'LOAD_TASKS', payload: JSON.parse(tasks) });
    }
  } catch (error) {
    console.error('Failed to load tasks:', error);
  }
};
