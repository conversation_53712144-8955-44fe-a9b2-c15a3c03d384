// API Configuration for EventMate App
// 
// To use real Ticketmaster data:
// 1. Go to https://developer.ticketmaster.com/products-and-docs/apis/getting-started/
// 2. Create a free account and get your API key
// 3. Replace 'YOUR_API_KEY' below with your actual API key

export const TICKETMASTER_API_KEY = 'YOUR_API_KEY';
export const TICKETMASTER_BASE_URL = 'https://app.ticketmaster.com/discovery/v2';

// Mock data for demonstration purposes
export const MOCK_EVENTS = [
  {
    id: '1',
    name: 'Toronto Music Festival 2025',
    dates: { start: { localDate: '2025-08-15' } },
    _embedded: { venues: [{ name: 'Harbourfront Centre, Toronto' }] },
    info: 'Annual music festival featuring top Canadian and international artists.',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Music+Festival' }]
  },
  {
    id: '2',
    name: 'Blue Jays vs Yankees',
    dates: { start: { localDate: '2025-08-20' } },
    _embedded: { venues: [{ name: 'Rogers Centre, Toronto' }] },
    info: 'Exciting baseball game between Toronto Blue Jays and New York Yankees.',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Baseball+Game' }]
  },
  {
    id: '3',
    name: 'Comedy Night Live',
    dates: { start: { localDate: '2025-08-25' } },
    _embedded: { venues: [{ name: 'Second City Toronto' }] },
    info: 'Stand-up comedy show featuring local and touring comedians.',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Comedy+Show' }]
  },
  {
    id: '4',
    name: 'Art Gallery Exhibition',
    dates: { start: { localDate: '2025-09-01' } },
    _embedded: { venues: [{ name: 'Art Gallery of Ontario' }] },
    info: 'Contemporary art exhibition showcasing emerging Canadian artists.',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Art+Exhibition' }]
  },
  {
    id: '5',
    name: 'Food & Wine Festival',
    dates: { start: { localDate: '2025-09-10' } },
    _embedded: { venues: [{ name: 'Distillery District, Toronto' }] },
    info: 'Culinary event featuring local restaurants and wineries.',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Food+Festival' }]
  }
];

export const getMockSearchResults = (query: string) => [
  {
    id: 'search1',
    name: `${query} Concert Experience`,
    dates: { start: { localDate: '2025-08-25' } },
    _embedded: { venues: [{ name: 'Phoenix Concert Theatre, Toronto' }] },
    info: `Live ${query} performance with special guests.`,
    images: [{ url: 'https://via.placeholder.com/300x200?text=Concert' }]
  },
  {
    id: 'search2',
    name: `${query} Festival 2025`,
    dates: { start: { localDate: '2025-09-05' } },
    _embedded: { venues: [{ name: 'Exhibition Place, Toronto' }] },
    info: `Annual ${query} festival featuring multiple artists and activities.`,
    images: [{ url: 'https://via.placeholder.com/300x200?text=Festival' }]
  },
  {
    id: 'search3',
    name: `${query} Workshop & Show`,
    dates: { start: { localDate: '2025-09-15' } },
    _embedded: { venues: [{ name: 'Roy Thomson Hall, Toronto' }] },
    info: `Interactive ${query} workshop followed by live performance.`,
    images: [{ url: 'https://via.placeholder.com/300x200?text=Workshop' }]
  }
];
