import { registerRootComponent } from 'expo';

import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);

// Install necessary packages for navigation
// npm install @react-navigation/native @react-navigation/stack
// npm install react-native-screens react-native-safe-area-context
