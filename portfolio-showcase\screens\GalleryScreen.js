import React from 'react';
import { View, StyleSheet, Text, Image } from 'react-native';
import Carousel from 'react-native-snap-carousel';
import { Dimensions } from 'react-native';

const projects = [
  {
    title: 'Project 1',
    description: 'Amazing design and UX',
    image: require('../assets/images/project1.jpg')
  },
  {
    title: 'Project 2',
    description: 'Creative branding solution',
    image: require('../assets/images/project2.jpg')
  }
  // Add more projects as needed
];

const windowWidth = Dimensions.get('window').width;

export default function GalleryScreen() {
  const renderItem = ({ item }) => (
    <View style={styles.card}>
      <Image source={item.image} style={styles.image} />
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.desc}>{item.description}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Carousel
        data={projects}
        renderItem={renderItem}
        sliderWidth={windowWidth}
        itemWidth={windowWidth * 0.75}
        layout="default"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, paddingTop: 20 },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    elevation: 3
  },
  image: {
    width: '100%',
    height: 200,
    borderRadius: 8
  },
  title: {
    fontSize: 20,
    marginTop: 10,
    fontWeight: 'bold'
  },
  desc: {
    fontSize: 14,
    marginTop: 5,
    color: '#555'
  }
});
