import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, FlatList, TouchableOpacity } from 'react-native';
import axios from 'axios';
import EventCard from './EventCard';
import { TICKETMASTER_API_KEY, TICKETMASTER_BASE_URL, getMockSearchResults } from '../config/api';

export default function SearchScreen({ navigation }: any) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const searchEvents = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      if (TICKETMASTER_API_KEY === 'YOUR_API_KEY') {
        // Using mock search results for demonstration
        console.log('Using mock search data - Please add your Ticketmaster API key in src/config/api.ts for real search');
        setSearchResults(getMockSearchResults(searchQuery));
        setLoading(false);
        return;
      }

      const res = await axios.get(
        `${TICKETMASTER_BASE_URL}/events.json?apikey=${TICKETMASTER_API_KEY}&keyword=${searchQuery}&countryCode=CA&size=20`
      );
      setSearchResults(res.data._embedded?.events || []);
    } catch (error) {
      console.log('Search API request failed, using mock data for demonstration');
      // Fallback mock search results
      setSearchResults(getMockSearchResults(searchQuery));
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search for events..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={searchEvents}
        />
        <TouchableOpacity style={styles.searchButton} onPress={searchEvents}>
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>

      {loading && <Text style={styles.loadingText}>Searching...</Text>}

      <FlatList
        data={searchResults}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <EventCard
            event={item}
            onPress={() => navigation.navigate('EventDetail', { event: item })}
          />
        )}
        ListEmptyComponent={
          !loading && searchQuery ? (
            <Text style={styles.emptyText}>No events found for "{searchQuery}"</Text>
          ) : (
            <Text style={styles.emptyText}>Enter a search term to find events</Text>
          )
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
  searchContainer: { flexDirection: 'row', marginBottom: 15 },
  searchInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 8,
    marginRight: 10
  },
  searchButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 8,
    justifyContent: 'center'
  },
  searchButtonText: { color: 'white', fontWeight: 'bold' },
  loadingText: { textAlign: 'center', marginVertical: 20 },
  emptyText: { textAlign: 'center', marginTop: 50, color: '#666' },
});
