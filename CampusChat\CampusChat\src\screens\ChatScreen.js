import React, { useEffect, useRef, useState } from 'react';
import { View, FlatList, StyleSheet, ActivityIndicator } from 'react-native';
import { firestore } from '../services/firebase';
import { useAuth } from '../services/auth';
import MessageItem from '../components/MessageItem';
import MessageInput from '../components/MessageInput';

export default function ChatScreen({ route }) {
  const { channel } = route.params;
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const listRef = useRef(null);

  useEffect(() => {
    const unsub = firestore()
      .collection('messages')
      .where('channelId', '==', channel.id)
      .orderBy('createdAt', 'asc')
      .onSnapshot((snapshot) => {
        const data = snapshot.docs.map(d => ({ id: d.id, ...d.data() }));
        setMessages(data);
        setLoading(false);
        setTimeout(() => listRef.current?.scrollToEnd({ animated: false }), 0);
      });
    return () => unsub();
  }, [channel.id]);

  const sendMessage = async (text) => {
    if (!text.trim()) return;
    await firestore().collection('messages').add({
      text: text.trim(),
      userId: user.uid,
      username: user.displayName ?? user.email?.split('@')[0] ?? 'user',
      channelId: channel.id,
      createdAt: firestore.FieldValue.serverTimestamp(),
    });
  };

  if (loading) return <View style={styles.center}><ActivityIndicator /></View>;

  return (
    <View style={styles.container}>
      <FlatList
        ref={listRef}
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <MessageItem message={item} />}
        contentContainerStyle={{ padding: 12 }}
        onContentSizeChange={() => listRef.current?.scrollToEnd({ animated: true })}
      />
      <MessageInput onSend={sendMessage} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  center: { flex: 1, justifyContent: 'center', alignItems: 'center' }
});
