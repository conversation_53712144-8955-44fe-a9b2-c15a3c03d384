import React, { createContext, useState, useContext } from 'react';

const EventContext = createContext<any>(null);

export const EventProvider = ({ children }: any) => {
  const [events, setEvents] = useState([]);
  const [favorites, setFavorites] = useState<any[]>([]);

  const toggleFavorite = (event: any) => {
    const exists = favorites.find((e) => e.id === event.id);
    if (exists) {
      setFavorites(favorites.filter((e) => e.id !== event.id));
    } else {
      setFavorites([...favorites, event]);
    }
  };

  return (
    <EventContext.Provider value={{ events, setEvents, favorites, toggleFavorite }}>
      {children}
    </EventContext.Provider>
  );
};

export const useEventContext = () => useContext(EventContext);
