# CampusChat — Discord-Style Chat (React Native + Firebase)

A minimal Discord-style chat app for the assignment. Implements:
- Email/password **Auth**
- Channel list with **default channels** (#general, #study-groups, #announcements, #random)
- **Realtime** messages per channel via Firestore
- Simple, responsive RN UI with navigation

---

## 1) Prerequisites

- Node.js 18+ and JDK 17
- Android Studio + Android SDK (Android 14 or 13 recommended)
- Watchman (macOS), CocoaPods (for iOS)
- A physical Android device or emulator

## 2) Create the project (two options)

### Option A — Start from scratch (recommended)
```bash
# Create a new bare RN project
npx react-native init CampusChat --version 0.75.3
cd CampusChat

# Install navigation + Firebase libs
npm i @react-navigation/native @react-navigation/native-stack
npm i @react-native-firebase/app @react-native-firebase/auth @react-native-firebase/firestore
npm i react-native-gesture-handler react-native-safe-area-context react-native-screens date-fns

# iOS only (macOS):
cd ios && pod install && cd ..
```

Then **replace** the generated `App.js` and add the `src/` folder with the files from this repository.

### Option B — Use the files in this ZIP
Download this repo, run:
```bash
npm i
# iOS: cd ios && pod install && cd ..
```

> If you used Option B, ensure Android/iOS native configs are in place (next steps).

## 3) Firebase Setup

1. Go to https://console.firebase.google.com → Add project
2. Add an **Android app** with package name `com.campuschat` (or your chosen package)
3. Download the generated **google-services.json**
4. Place it at: `android/app/google-services.json`
5. Enable **Email/Password** in Firebase Authentication
6. Create a **Cloud Firestore** database (Start in production or test)
7. (Optional) Add an **iOS app** and download `GoogleService-Info.plist` → put it in `ios/` and add to Xcode project.

### Android Gradle changes (usually auto by RNFirebase):
- `android/build.gradle`:
  ```gradle
  buildscript {
      dependencies {
          classpath('com.google.gms:google-services:4.4.2')
      }
  }
  ```
- `android/app/build.gradle`:
  ```gradle
  apply plugin: 'com.google.gms.google-services'
  ```

## 4) Run the app

### Android
```bash
npm run android
# or
npx react-native run-android
```

### iOS (macOS)
```bash
cd ios && pod install && cd ..
npm run ios
```

## 5) Firestore Rules (simple dev rules)
In Firebase console → Firestore → Rules:
```
// *** DEV ONLY — loosened rules for assignment testing ***
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```
Remember to tighten rules for production.

## 6) Project Structure

```
src/
  components/
    ChannelItem.js
    MessageInput.js
    MessageItem.js
  navigation/
    AppNavigator.js
  screens/
    ChannelListScreen.js
    ChatScreen.js
    LoginScreen.js
  services/
    auth.js
    firebase.js
App.js
```

## 7) Default Channels
On first load, if the `channels` collection is empty, the app seeds:
- `#general`, `#study-groups`, `#announcements`, `#random`

## 8) Common Issues

- **Google services not found**: Ensure `android/app/google-services.json` exists and package name matches.
- **iOS pods**: Run `cd ios && pod install` after installing RNFirebase.
- **Hermes/Reanimated**: If Reanimated warns, rerun Metro: `npm start -- --reset-cache`.

---

### Bonus Ideas (Optional)
- Image upload via Firebase Storage
- Emoji reactions
- Presence/online status
- Dark mode toggle

Good luck!
