import React, { useEffect } from 'react';
import { View, FlatList, StyleSheet, RefreshControl } from 'react-native';
import axios from 'axios';
import { useEventContext } from './EventContext';
import EventCard from './EventCard';
import { TICKETMASTER_API_KEY, TICKETMASTER_BASE_URL, MOCK_EVENTS } from '../config/api';

export default function HomeScreen({ navigation }: any) {
  const { events, setEvents } = useEventContext();

  const fetchEvents = async () => {
    try {
      if (TICKETMASTER_API_KEY === 'YOUR_API_KEY') {
        // Using mock data for demonstration since no real API key is provided
        console.log('Using mock data - Please add your Ticketmaster API key in src/config/api.ts for real data');
        setEvents(MOCK_EVENTS);
        return;
      }

      const res = await axios.get(
        `${TICKETMASTER_BASE_URL}/events.json?apikey=${TICKETMASTER_API_KEY}&countryCode=CA&size=20`
      );
      setEvents(res.data._embedded?.events || []);
    } catch (error) {
      console.log('API request failed, using mock data for demonstration');
      // Fallback to mock data if API fails
      setEvents(MOCK_EVENTS);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  return (
    <View style={styles.container}>
      <FlatList
        data={events}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <EventCard event={item} onPress={() => navigation.navigate('EventDetail', { event: item })} />
        )}
        refreshControl={<RefreshControl refreshing={false} onRefresh={fetchEvents} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
});
