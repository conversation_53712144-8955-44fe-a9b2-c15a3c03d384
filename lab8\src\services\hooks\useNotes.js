import { useEffect, useState } from "react";
import { db } from "../services/firebase";
import {
  collection, query, where, orderBy,
  onSnapshot, addDoc, updateDoc, deleteDoc, doc, serverTimestamp
} from "firebase/firestore";

export function useNotes(user, searchTerm="", tagFilter=[]) {
  const [notes, setNotes] = useState([]);

  useEffect(() => {
    if (!user) return setNotes([]);

    let q = query(
      collection(db, "notes"),
      where("uid", "==", user.uid),
      orderBy("updatedAt", "desc")
    );
    const unsubscribe = onSnapshot(q, snap => {
      let arr = [];
      snap.forEach(d => {
        const data = d.data();
        if ((searchTerm && !data.title.includes(searchTerm) && !data.body.includes(searchTerm)) ||
            (tagFilter.length && !tagFilter.some(t => data.tags?.includes(t)))) return;
        arr.push({ id: d.id, ...data });
      });
      setNotes(arr);
    }, console.error);

    return unsubscribe;
  }, [user, searchTerm, tagFilter]);

  const create = async ({ title="", body="", tags=[] }) => {
    await addDoc(collection(db, "notes"), {
      uid: user.uid,
      title, body, tags,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  };

  const update = async (id, fields) => {
    await updateDoc(doc(db, "notes", id), {
      ...fields, updatedAt: serverTimestamp()
    });
  };

  const remove = id => deleteDoc(doc(db, "notes", id));

  return { notes, create, update, remove };
}
