import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import HomeScreen from './src/screens/HomeScreen';
import SearchScreen from './src/screens/SearchScreen';
import FavoritesScreen from './src/screens/FavoritesScreen';
import AboutScreen from './src/screens/AboutScreen';
import EventScreen from './src/context/EventScreen'; // <-- Add this import

const Tab = createBottomTabNavigator<TabParamList>();

// Define the parameter list for the tab navigator
export type TabParamList = {
  Home: undefined;
  Search: undefined;
  Favorites: undefined;
  About: undefined;
  Event: undefined;
};

function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Search') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'About') {
            iconName = focused ? 'information-circle' : 'information-circle-outline';
          } else if (route.name === 'Event') { // <-- Add this block
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else {
            iconName = 'home-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      {/* ...existing Tab.Screens... */}
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ title: 'EventMate' }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: 'Search Events' }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoritesScreen}
        options={{ title: 'My Favorites' }}
      />
      <Tab.Screen
        name="About"
        component={AboutScreen}
        options={{ title: 'About App' }}
      />
      <Tab.Screen
        name="Event"
        component={EventScreen}
        options={{ title: 'Event' }}
      />
    </Tab.Navigator>
  );
}

export default MainTabs;