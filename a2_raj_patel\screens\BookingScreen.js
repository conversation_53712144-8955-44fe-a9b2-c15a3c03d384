import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const BookingScreen = ({ route }) => {
  const { studentId, name, numPeople, room } = route.params;

  const rooms = [
    { roomNumber: 'A101', capacity: 5, available: true },
    { roomNumber: 'A102', capacity: 10, available: false },
    { roomNumber: 'A103', capacity: 8, available: false },
    { roomNumber: 'A104', capacity: 10, available: true },
    { roomNumber: 'A105', capacity: 7, available: true }
  ];

  const selectedRoom = rooms.find(r => r.roomNumber === room);
  const message = selectedRoom
    ? selectedRoom.available && selectedRoom.capacity >= numPeople
      ? '✅ Room is available!'
      : '❌ Room is not available or not enough capacity.'
    : 'Room not found.';

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Booking Info</Text>
      <Text>Student ID: {studentId}</Text>
      <Text>Name: {name}</Text>
      <Text>Room: {room}</Text>
      <Text>Number of People: {numPeople}</Text>
      <Text style={{ marginTop: 20, fontWeight: 'bold' }}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },
  title: { fontSize: 24, marginBottom: 20, textAlign: 'center' }
});

export default BookingScreen;
