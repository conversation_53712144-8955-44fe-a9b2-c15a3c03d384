import React from 'react';
import { render } from '@testing-library/react-native';
import HomeScreen from '../../screens/HomeScreen';

// Mock the SocialLinks component
jest.mock('../../components/SocialLinks', () => ({
  SocialLinks: () => 'SocialLinks'
}));

describe('HomeScreen', () => {
  test('renders correctly', () => {
    const { getByText } = render(<HomeScreen />);
    
    expect(getByText('Welcome to My Portfolio!')).toBeTruthy();
    expect(getByText(/Hi, I'm a passionate creative professional/)).toBeTruthy();
  });

  test('displays welcome heading', () => {
    const { getByText } = render(<HomeScreen />);
    const heading = getByText('Welcome to My Portfolio!');
    expect(heading).toBeTruthy();
  });

  test('displays description paragraph', () => {
    const { getByText } = render(<HomeScreen />);
    const description = getByText(/Check out my projects and feel free to get in touch/);
    expect(description).toBeTruthy();
  });
});
