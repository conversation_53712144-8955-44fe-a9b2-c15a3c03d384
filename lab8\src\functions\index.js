const functions = require("firebase-functions");
const admin = require("firebase-admin");
admin.initializeApp();

exports.indexNote = functions.firestore
  .document("notes/{noteId}")
  .onWrite((change, ctx) => {
    const note = change.after.exists ? change.after.data() : null;
    const tags = note?.tags || [];
    const db = admin.firestore();
    const tagIndex = db.collection("noteTags").doc(ctx.params.noteId);
    return note
      ? tagIndex.set({ uid: note.uid, tags })
      : tagIndex.delete();
  });

exports.cleanupUserNotes = functions.auth.user().onDelete(async user => {
  const db = admin.firestore();
  const snaps = await db.collection("notes").where("uid", "==", user.uid).get();
  const batch = db.batch();
  snaps.forEach(d => batch.delete(d.ref));
  return batch.commit();
});
