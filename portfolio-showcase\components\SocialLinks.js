import React from 'react';
import { View, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const socialMedia = [
  { name: 'github', url: 'https://github.com/raj271197' },
  { name: 'linkedin', url: 'https://ca.linkedin.com/' },
  { name: 'twitter', url: 'https://twitter.com/yourhandle' }
];

export function SocialLinks() {
  return (
    <View style={styles.container}>
      {socialMedia.map(({ name, url }) => (
        <TouchableOpacity
          key={name}
          onPress={() => Linking.openURL(url)}
          style={styles.iconButton}
          accessibilityLabel={`Open ${name} profile`}
        >
          <MaterialCommunityIcons name={name} size={36} color="#0077b5" />
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flexDirection: 'row', justifyContent: 'center', gap: 15 },
  iconButton: { marginHorizontal: 10 }
});
