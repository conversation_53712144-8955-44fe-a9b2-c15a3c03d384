{"name": "portfolio-showcase", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "expo": "53", "expo-mail-composer": "^14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-snap-carousel": "^3.9.1"}, "devDependencies": {"@babel/core": "^7.22.10", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "jest": "^30.0.5", "jest-expo": "^53.0.9", "react-test-renderer": "^19.0.0"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}