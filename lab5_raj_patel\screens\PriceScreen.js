import React from 'react';
import { View, Text, Button, StyleSheet } from 'react-native';

const PriceScreen = ({ route, navigation }) => {
  const { quantity } = route.params;
  const price = quantity * 5;

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Quantity: {quantity}</Text>
      <Text style={styles.text}>Total Price: ${price.toFixed(2)}</Text>
      <Button
        title="Next"
        onPress={() => navigation.navigate('FinalBill', { price })}
      />
    </View>
  );
};

export default PriceScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1, justifyContent: 'center', alignItems: 'center',
  },
  text: {
    fontSize: 20, marginBottom: 20,
  },
});
