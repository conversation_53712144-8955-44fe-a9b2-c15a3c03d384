import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';

const CircularProgress = ({ fill, title }) => {
  return (
    <View style={styles.container}>
      <AnimatedCircularProgress
        size={120}
        width={15}
        fill={fill}
        tintColor="#00e0ff"
        backgroundColor="#3d5875"
      >
        {(fill) => <Text>{`${Math.round(fill)}%`}</Text>}
      </AnimatedCircularProgress>
      <Text>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
});

export default CircularProgress;
