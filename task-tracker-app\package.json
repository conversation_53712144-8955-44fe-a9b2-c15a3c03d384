{"name": "task-tracker-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.20", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "@react-native-async-storage/async-storage": "2.1.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}