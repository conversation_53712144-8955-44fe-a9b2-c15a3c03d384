import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { DarkModeToggle } from '../../components/DarkModeToggle';
import { ThemeContext } from '../../App';

const mockThemeContext = {
  isDark: false,
  setIsDark: jest.fn()
};

const renderWithTheme = (component, themeValue = mockThemeContext) => {
  return render(
    <ThemeContext.Provider value={themeValue}>
      {component}
    </ThemeContext.Provider>
  );
};

describe('DarkModeToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly', () => {
    const { getByRole } = renderWithTheme(<DarkModeToggle />);
    const toggle = getByRole('switch');
    expect(toggle).toBeTruthy();
  });

  test('displays correct initial state', () => {
    const { getByRole } = renderWithTheme(<DarkModeToggle />);
    const toggle = getByRole('switch');
    expect(toggle.props.value).toBe(false);
  });

  test('calls setIsDark when toggled', () => {
    const { getByRole } = renderWithTheme(<DarkModeToggle />);
    const toggle = getByRole('switch');
    
    fireEvent(toggle, 'valueChange', true);
    expect(mockThemeContext.setIsDark).toHaveBeenCalledWith(true);
  });

  test('displays dark mode when isDark is true', () => {
    const darkThemeContext = { ...mockThemeContext, isDark: true };
    const { getByRole } = renderWithTheme(<DarkModeToggle />, darkThemeContext);
    const toggle = getByRole('switch');
    expect(toggle.props.value).toBe(true);
  });
});
