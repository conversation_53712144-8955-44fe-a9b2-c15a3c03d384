{"name": "CampusChat", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.75.3", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.10.1", "@react-native-firebase/app": "^20.3.0", "@react-native-firebase/auth": "^20.3.0", "@react-native-firebase/firestore": "^20.3.0", "react-native-gesture-handler": "^2.18.0", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.31.1", "date-fns": "^3.6.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/runtime": "^7.25.0", "@react-native/eslint-config": "^0.76.0", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.78.8", "react-test-renderer": "18.2.0"}, "jest": {"preset": "react-native"}}