import React from 'react';
import { View, Text, FlatList, StyleSheet } from 'react-native';

const ScoreboardScreen = ({ route }) => {
  const { gameResults } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Scoreboard</Text>
      <FlatList
        data={gameResults}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({ item, index }) => (
          <View style={[styles.item, item.result && styles.wonItem]}>
            <Text>Game {index + 1}: {item.result ? 'Won' : 'Lost'} in {item.attempts} attempts</Text>
          </View>
        )}
      />
    </View>
  );
};

export default ScoreboardScreen;

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },
  title: { fontSize: 22, fontWeight: 'bold', textAlign: 'center', marginBottom: 10 },
  item: { padding: 15, borderBottomWidth: 1, borderColor: '#ccc' },
  wonItem: { backgroundColor: '#ccffcc' }
});
