// App.js
import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header */}
      <Text style={styles.header}>Now Playing</Text>

      {/* Podcast Image */}
      <Image
        source={{ uri: 'https://cdn-icons-png.flaticon.com/512/727/727240.png' }}
        style={styles.image}
      />

      {/* Podcast Title and Artist */}
      <Text style={styles.title}>Podcast Episode Title</Text>
      <Text style={styles.artist}>Podcast Host Name</Text>

      {/* Progress Bar */}
      <View style={styles.progressBar}>
        <View style={styles.progressFill}></View>
      </View>

      {/* Time Stamps */}
      <View style={styles.timestamps}>
        <Text style={styles.time}>1:12</Text>
        <Text style={styles.time}>3:45</Text>
      </View>

      {/* Player Controls */}
      <View style={styles.controls}>
        <TouchableOpacity>
          <Image
            source={{ uri: 'https://cdn-icons-png.flaticon.com/512/860/860790.png' }}
            style={styles.icon}
          />
        </TouchableOpacity>

        <TouchableOpacity>
          <Image
            source={{ uri: 'https://cdn-icons-png.flaticon.com/512/727/727245.png' }}
            style={[styles.icon, { width: 60, height: 60 }]}
          />
        </TouchableOpacity>

        <TouchableOpacity>
          <Image
            source={{ uri: 'https://cdn-icons-png.flaticon.com/512/860/860828.png' }}
            style={styles.icon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F0',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  image: {
    width: 250,
    height: 250,
    borderRadius: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 10,
  },
  artist: {
    fontSize: 16,
    color: 'gray',
    textAlign: 'center',
    marginBottom: 20,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#ddd',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 10,
  },
  progressFill: {
    width: '40%',
    height: '100%',
    backgroundColor: '#f76c6c',
  },
  timestamps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 5,
    marginBottom: 30,
  },
  time: {
    color: 'gray',
    fontSize: 12,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '60%',
    alignItems: 'center',
  },
  icon: {
    width: 40,
    height: 40,
    tintColor: '#444',
  },
});
