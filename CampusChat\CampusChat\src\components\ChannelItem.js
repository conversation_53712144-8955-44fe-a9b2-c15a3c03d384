import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';

export default function ChannelItem({ channel, onPress }) {
  return (
    <TouchableOpacity onPress={onPress} style={styles.card}>
      <View>
        <Text style={styles.name}>{channel.name}</Text>
        {channel.description ? <Text style={styles.desc}>{channel.description}</Text> : null}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: { padding: 16, backgroundColor: '#f3f4f6', borderRadius: 12 },
  name: { fontSize: 18, fontWeight: '800' },
  desc: { color: '#6b7280', marginTop: 4 }
});
