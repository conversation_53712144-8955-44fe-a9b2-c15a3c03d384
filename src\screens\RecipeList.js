import React, { useState, useRef } from 'react';
import { 
  View, 
  FlatList, 
  StyleSheet, 
  RefreshControl,
  Animated 
} from 'react-native';
import RecipeCard from '../components/RecipeCard';
import SwipeableCard from '../animations/SwipeableCard';

const SAMPLE_RECIPES = [
  { id: '1', title: 'Pasta Carbonara', image: 'https://via.placeholder.com/300x200' },
  { id: '2', title: 'Chicken Tikka', image: 'https://via.placeholder.com/300x200' },
  { id: '3', title: 'Beef Stir Fry', image: 'https://via.placeholder.com/300x200' },
  { id: '4', title: 'Vegetable Curry', image: 'https://via.placeholder.com/300x200' },
];

const RecipeList = ({ navigation }) => {
  const [recipes, setRecipes] = useState(SAMPLE_RECIPES);
  const [refreshing, setRefreshing] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  const renderRecipe = ({ item, index }) => (
    <SwipeableCard
      onPress={() => navigation.navigate('RecipeDetail', { recipe: item })}
      onSwipeRight={() => console.log('Favorited:', item.title)}
    >
      <Animated.View
        style={{
          transform: [{
            translateY: scrollY.interpolate({
              inputRange: [0, index * 100, (index + 1) * 100],
              outputRange: [0, 0, -50],
              extrapolate: 'clamp',
            }),
          }],
        }}
      >
        <RecipeCard recipe={item} />
      </Animated.View>
    </SwipeableCard>
  );

  return (
    <View style={styles.container}>
      <Animated.FlatList
        data={recipes}
        renderItem={renderRecipe}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingTop: 50,
  },
});

export default RecipeList;