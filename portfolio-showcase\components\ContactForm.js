import React, { useState } from 'react';
import { View, TextInput, Button, Alert, StyleSheet, Text } from 'react-native';
import * as MailComposer from 'expo-mail-composer';

export default function ContactForm() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');

  const handleSendEmail = () => {
    if (!name || !email || !message) {
      Alert.alert('Validation Error', 'Please fill all fields');
      return;
    }
    const options = {
      recipients: ['<EMAIL>'],
      subject: `Portfolio Contact from ${name}`,
      body: `Name: ${name}\nEmail: ${email}\n\nMessage:\n${message}`
    };
    MailComposer.composeAsync(options)
      .then((result) => {
        if (result.status === 'sent') {
          Alert.alert('Success', 'Email sent successfully!');
          setName('');
          setEmail('');
          setMessage('');
        }
      })
      .catch(() => Alert.alert('Error', 'Failed to send email.'));
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Contact Me</Text>
      <TextInput
        placeholder="Your Name"
        style={styles.input}
        value={name}
        onChangeText={setName}
        autoCapitalize="words"
      />
      <TextInput
        placeholder="Your Email"
        style={styles.input}
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        placeholder="Message"
        style={[styles.input, styles.textArea]}
        value={message}
        onChangeText={setMessage}
        multiline
        numberOfLines={4}
      />
      <Button title="Send" onPress={handleSendEmail} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { padding: 20 },
  heading: { fontSize: 24, fontWeight: 'bold', marginBottom: 15 },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    marginBottom: 15,
    borderRadius: 5
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top'
  }
});
