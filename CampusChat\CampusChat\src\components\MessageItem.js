import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { format } from 'date-fns';

export default function MessageItem({ message }) {
  const createdAt = message?.createdAt?.toDate ? message.createdAt.toDate() : new Date();
  return (
    <View style={styles.wrapper}>
      <Text style={styles.username}>{message.username}</Text>
      <Text style={styles.text}>{message.text}</Text>
      <Text style={styles.time}>{format(createdAt, 'MMM d, h:mm a')}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: { backgroundColor: '#f3f4f6', padding: 10, borderRadius: 10, marginBottom: 8 },
  username: { fontWeight: '700' },
  text: { marginTop: 4, fontSize: 16 },
  time: { marginTop: 6, color: '#6b7280', fontSize: 12, alignSelf: 'flex-end' }
});
