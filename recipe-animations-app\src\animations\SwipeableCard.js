import React from 'react';
import { View, StyleSheet, Animated, Text } from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';

const SwipeableCard = ({ children }) => {
  const translateX = new Animated.Value(0);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  return (
    <PanGestureHandler onGestureEvent={onGestureEvent}>
      <Animated.View style={[styles.card, { transform: [{ translateX }] }]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 20,
    margin: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    elevation: 5,
  },
});

export default SwipeableCard;
