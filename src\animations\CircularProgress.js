import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';

const CircularProgress = ({ fill = 0, title = "Progress" }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const circleRef = useRef();

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: fill,
      duration: 2000,
      useNativeDriver: false,
    }).start();
  }, [fill]);

  useEffect(() => {
    animatedValue.addListener((value) => {
      if (circleRef.current) {
        const strokeDashoffset = 251.2 - (251.2 * value.value) / 100;
        circleRef.current.setNativeProps({
          strokeDashoffset,
        });
      }
    });

    return () => animatedValue.removeAllListeners();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.progressContainer}>
        <Animated.Text style={styles.percentage}>
          {animatedValue.interpolate({
            inputRange: [0, 100],
            outputRange: ['0%', `${fill}%`],
            extrapolate: 'clamp',
          })}
        </Animated.Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    margin: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  progressContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 8,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  percentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
});

export default CircularProgress;