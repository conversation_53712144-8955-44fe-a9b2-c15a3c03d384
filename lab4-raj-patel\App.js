import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

export default function App() {
  const [selectedIndustry, setSelectedIndustry] = useState('dev');
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchJobs = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `https://jobicy.com/api/v2/remote-jobs?count=20&industry=${selectedIndustry}`
      );
      const data = await response.json();
      setJobs(data.jobs || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchJobs();
  }, [selectedIndustry]);

  const renderItem = ({ item }) => (
    <View style={styles.card}>
      {item.companyLogo && (
        <Image source={{ uri: item.companyLogo }} style={styles.logo} />
      )}
      <Text style={styles.title}>{item.jobTitle || 'No Title'}</Text>
      <Text>Company: {item.companyName || 'N/A'}</Text>
      <Text>Type: {item.jobType || 'N/A'}</Text>
      <Text>Level: {item.jobLevel || 'N/A'}</Text>
      <Text>Industry: {item.jobIndustry || 'N/A'}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Select Industry</Text>

      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={selectedIndustry}
          onValueChange={(value) => setSelectedIndustry(value)}
          style={styles.picker}
          mode="dropdown"
        >
          <Picker.Item label="Dev" value="dev" />
          <Picker.Item label="HR" value="hr" />
          <Picker.Item label="Business" value="business" />
        </Picker>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#000" />
      ) : (
        <FlatList
          data={jobs}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderItem}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 50,
    paddingHorizontal: 10,
    flex: 1,
    backgroundColor: '#fff',
  },
  heading: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    marginBottom: 20,
    overflow: 'hidden',
  },
  picker: {
    height: Platform.OS === 'ios' ? 200 : 50,
    width: '100%',
  },
  card: {
    backgroundColor: '#f2f2f2',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 10,
    resizeMode: 'contain',
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
});
