import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert } from 'react-native';
import { Picker } from '@react-native-picker/picker';

const DashboardScreen = ({ navigation }) => {
  const [studentId, setStudentId] = useState('');
  const [name, setName] = useState('');
  const [numPeople, setNumPeople] = useState('');
  const [room, setRoom] = useState('A101');

  const handleCheck = () => {
    if (!studentId || !name || !numPeople) {
      Alert.alert('Please fill all fields');
      return;
    }

    navigation.navigate('Booking', {
      studentId,
      name,
      numPeople: parseInt(numPeople),
      room
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Dashboard</Text>
      <TextInput placeholder="Student ID" style={styles.input} onChangeText={setStudentId} />
      <TextInput placeholder="Name" style={styles.input} onChangeText={setName} />
      <TextInput placeholder="Number of People" keyboardType="numeric" style={styles.input} onChangeText={setNumPeople} />
      <Text style={styles.label}>Select Room:</Text>
      <View style={styles.pickerWrapper}>
        <Picker selectedValue={room} onValueChange={(value) => setRoom(value)}>
          <Picker.Item label="A101" value="A101" />
          <Picker.Item label="A102" value="A102" />
          <Picker.Item label="A103" value="A103" />
          <Picker.Item label="A104" value="A104" />
          <Picker.Item label="A105" value="A105" />
        </Picker>
      </View>
      <Button title="Check Availability" onPress={handleCheck} />
      <View style={{ marginTop: 10 }}>
        <Button title="Logout" color="red" onPress={() => navigation.navigate('SignIn')} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },
  title: { fontSize: 24, marginBottom: 20, textAlign: 'center' },
  input: { borderWidth: 1, marginBottom: 10, padding: 10, borderRadius: 5 },
  label: { fontSize: 16, marginBottom: 5 },
  pickerWrapper: { borderWidth: 1, borderRadius: 5, marginBottom: 10 }
});

export default DashboardScreen;
