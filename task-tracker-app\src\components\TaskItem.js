import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet } from 'react-native';
import useTasks from '../hooks/useTasks';

export default function TaskItem({ task }) {
  const { dispatch } = useTasks();
  const [isEditing, setIsEditing] = useState(false);
  const [text, setText] = useState(task.title);

  const handleSave = () => {
    dispatch({ type: 'DELETE_TASK', payload: task.id });
    dispatch({
      type: 'ADD_TASK',
      payload: { ...task, title: text },
    });
    setIsEditing(false);
  };

  return (
    <View style={styles.container}>
      {isEditing ? (
        <>
          <TextInput value={text} onChangeText={setText} style={styles.input} />
          <Button title="Save" onPress={handleSave} />
        </>
      ) : (
        <>
          <Text
            style={[
              styles.text,
              { textDecorationLine: task.completed ? 'line-through' : 'none' },
            ]}
            onPress={() => dispatch({ type: 'TOGGLE_TASK', payload: task.id })}
          >
            {task.title}
          </Text>
          <View style={styles.actions}>
            <Button title="Edit" onPress={() => setIsEditing(true)} />
            <Button
              title="Delete"
              color="red"
              onPress={() => dispatch({ type: 'DELETE_TASK', payload: task.id })}
            />
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 10,
    marginVertical: 5,
    backgroundColor: '#eee',
    borderRadius: 5,
  },
  input: {
    borderBottomWidth: 1,
    marginBottom: 5,
  },
  text: {
    fontSize: 18,
  },
  actions: {
    flexDirection: 'row',
    gap: 10,
  },
});
