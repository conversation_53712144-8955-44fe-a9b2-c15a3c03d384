import React, { useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.3;

const SwipeableCard = ({ children, onPress, onSwipeRight, onSwipeLeft }) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const scale = useRef(new Animated.Value(1)).current;

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { 
      useNativeDriver: true,
      listener: (event) => {
        const { translationX } = event.nativeEvent;
        const progress = Math.abs(translationX) / SWIPE_THRESHOLD;
        
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: Math.max(0.3, 1 - progress * 0.7),
            duration: 0,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: Math.max(0.8, 1 - progress * 0.2),
            duration: 0,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
  );

  const onHandlerStateChange = (event) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX, velocityX } = event.nativeEvent;
      
      if (Math.abs(translationX) > SWIPE_THRESHOLD || Math.abs(velocityX) > 1000) {
        // Complete swipe
        const toValue = translationX > 0 ? SCREEN_WIDTH : -SCREEN_WIDTH;
        
        Animated.parallel([
          Animated.timing(translateX, {
            toValue,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start(() => {
          if (translationX > 0 && onSwipeRight) {
            onSwipeRight();
          } else if (translationX < 0 && onSwipeLeft) {
            onSwipeLeft();
          }
          // Reset card position
          translateX.setValue(0);
          opacity.setValue(1);
          scale.setValue(1);
        });
      } else {
        // Snap back
        Animated.parallel([
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }),
          Animated.timing(opacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
  };

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
    >
      <Animated.View 
        style={[
          styles.card, 
          { 
            transform: [
              { translateX },
              { scale }
            ],
            opacity 
          }
        ]}
      >
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default SwipeableCard;